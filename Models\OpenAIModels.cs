using Newtonsoft.Json;

namespace ZeroDateStrat.Models;

/// <summary>
/// Response from OpenAI API
/// </summary>
public class OpenAIResponse
{
    public string Content { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public int TokensUsed { get; set; }
    public DateTime ResponseTime { get; set; } = DateTime.UtcNow;
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// OpenAI API request models for JSON serialization
/// </summary>
public class OpenAIRequest
{
    [JsonProperty("model")]
    public string Model { get; set; } = "gpt-4o";

    [JsonProperty("messages")]
    public List<OpenAIMessage> Messages { get; set; } = new();

    [JsonProperty("max_tokens")]
    public int MaxTokens { get; set; } = 2000;

    [JsonProperty("temperature")]
    public double Temperature { get; set; } = 0.7;
}

public class OpenAIMessage
{
    [JsonProperty("role")]
    public string Role { get; set; } = string.Empty;

    [JsonProperty("content")]
    public string Content { get; set; } = string.Empty;
}

/// <summary>
/// OpenAI API response models for JSON deserialization
/// </summary>
public class OpenAIApiResponse
{
    [JsonProperty("id")]
    public string Id { get; set; } = string.Empty;

    [JsonProperty("object")]
    public string Object { get; set; } = string.Empty;

    [JsonProperty("created")]
    public long Created { get; set; }

    [JsonProperty("model")]
    public string Model { get; set; } = string.Empty;

    [JsonProperty("choices")]
    public List<OpenAIChoice> Choices { get; set; } = new();

    [JsonProperty("usage")]
    public OpenAIUsage? Usage { get; set; }

    [JsonProperty("error")]
    public OpenAIError? Error { get; set; }
}

public class OpenAIChoice
{
    [JsonProperty("index")]
    public int Index { get; set; }

    [JsonProperty("message")]
    public OpenAIMessage? Message { get; set; }

    [JsonProperty("finish_reason")]
    public string? FinishReason { get; set; }
}

public class OpenAIUsage
{
    [JsonProperty("prompt_tokens")]
    public int PromptTokens { get; set; }

    [JsonProperty("completion_tokens")]
    public int CompletionTokens { get; set; }

    [JsonProperty("total_tokens")]
    public int TotalTokens { get; set; }
}

public class OpenAIError
{
    [JsonProperty("message")]
    public string Message { get; set; } = string.Empty;

    [JsonProperty("type")]
    public string Type { get; set; } = string.Empty;

    [JsonProperty("param")]
    public string? Param { get; set; }

    [JsonProperty("code")]
    public string? Code { get; set; }
}

/// <summary>
/// Configuration for OpenAI service
/// </summary>
public class OpenAIConfiguration
{
    public bool Enabled { get; set; } = true;
    public string ApiKey { get; set; } = string.Empty;
    public string Model { get; set; } = "gpt-4o";
    public int MaxTokens { get; set; } = 2000;
    public double Temperature { get; set; } = 0.7;
    public string SystemPrompt { get; set; } = "You provide technical instructions and programming support for software agents.";
    public List<string> TriggerKeywords { get; set; } = new() { "!askchatgpt", "!chatgpt", "!gpt" };
    public bool EnableMentionTrigger { get; set; } = true;
    public bool EnableKeywordTrigger { get; set; } = true;
    public string ResponsePrefix { get; set; } = "🤖 **ChatGPT Response:**\n";
    public string ErrorMessage { get; set; } = "❌ Something went wrong while fetching the response. Please try again.";
    public string EmptyRequestMessage { get; set; } = "⚠️ Please provide a valid request after the mention or command.";
    public int MaxMessageLength { get; set; } = 2000;
}
