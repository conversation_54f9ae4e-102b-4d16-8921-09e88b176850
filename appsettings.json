{"Alpaca": {"ApiKey": "ENC:QUtSNlNMSUtTQjBOQ0JMMkNOTEI=", "SecretKey": "ENC:bWdSdzAyZDVYTmFiY1Vnb3BWbWIyMmZEb0NFVkxzanM3UXN3eXdKeg==", "BaseUrl": "https://api.alpaca.markets", "DataUrl": "https://data.alpaca.markets"}, "Polygon": {"ApiKey": "********************************", "BaseUrl": "https://api.polygon.io"}, "Trading": {"PrimarySymbol": "SPX", "BackupSymbol": "SPY", "MaxPositionSize": 1500, "MaxDailyLoss": 180, "RiskPerTrade": 0.01, "MaxPositionsPerDay": 3, "MinDaysToExpiration": 0, "MaxDaysToExpiration": 0, "EntryTimeStart": "09:45:00", "EntryTimeEnd": "10:30:00", "ManagementTime": "14:00:00", "ForceCloseTime": "15:45:00", "TradingEndTime": "16:00:00", "MinAccountEquity": 10000, "MinBuyingPower": 5000, "ProfitTargetPercent": 0.5, "StopLossPercent": 2.0, "RiskRewardThreshold": 0.15, "MaxPortfolioHeat": 0.05}, "MarketRegime": {"VixLowThreshold": 25, "VixHighThreshold": 55, "TrendLookbackPeriods": 20, "VolatilityLookbackPeriods": 14, "VolatilityCalculationDays": 30, "VolatilityForecastLookback": 60, "ATRPeriods": 14, "RSIPeriods": 14, "BollingerBandPeriods": 20, "BollingerBandStdDev": 2.0, "GarchAlpha": 0.1, "GarchBeta": 0.85, "GarchOmega": 1e-05, "MicrostructureLookbackHours": 6, "MultiTimeframeEnabled": true, "RegimeTransitionSensitivity": 0.7, "VolatilitySpikeThreshold": 1.5, "CorrelationBreakdownThreshold": 0.3, "UnusualActivityVolumeThreshold": 5.0, "SentimentExtremeThreshold": 60, "MarketBreadthLookbackDays": 20, "OptionsFlowLookbackHours": 4, "AllowHighVolatilityTrading": true, "HighVolatilityMaxPositions": 1, "HighVolatilityRiskReduction": 0.5}, "SyntheticVix": {"Note": "Direct access to VIX (CBOE index) is not available.", "SubstituteIndices": [{"Symbol": "VXX", "Type": "ETF", "Weight": 0.5, "Source": "alpaca"}, {"Symbol": "UVXY", "Type": "ETF", "Weight": 0.3, "Source": "alpaca"}, {"Symbol": "SVXY", "Type": "ETF", "Weight": -0.2, "Source": "alpaca"}], "Normalization": {"Method": "z-score", "Window": 20}, "CompositeIndexLabel": "SyntheticVIX", "Usage": "Use 'SyntheticVIX' in place of 'VIX' for volatility regime checks, signal thresholds, and entry/exit filters."}, "Risk": {"MaxDrawdown": 0.05, "VaRLimit": 0.02, "MaxConcentration": 0.5, "MaxCorrelatedExposure": 0.6, "PortfolioHeatLimit": 0.6, "MaxDailyTrades": 3, "MaxOpenPositions": 3, "StressTestMultiplier": 2.0, "RiskRewardMinimum": 0.2, "MaxPositionsPerSymbol": 2, "ConcentrationWarningLevel": 0.4}, "Strategies": {"PutCreditSpread": {"Enabled": true, "Priority": 1, "MinDelta": 0.05, "MaxDelta": 0.15, "MinPremium": 0.1, "MaxSpreadWidth": 10, "MinDaysToExpiration": 0, "MaxDaysToExpiration": 0, "ProfitTarget": 0.5, "StopLoss": 2.0}, "IronButterfly": {"Enabled": true, "Priority": 2, "ATMRange": 0.02, "WingWidth": 25, "MinPremium": 0.15, "ProfitTarget": 0.5, "StopLoss": 2.0}, "CallCreditSpread": {"Enabled": true, "Priority": 3, "MinDelta": 0.05, "MaxDelta": 0.15, "MinPremium": 0.1, "MaxSpreadWidth": 10, "ProfitTarget": 0.5, "StopLoss": 2.0}, "IronCondor": {"Enabled": true, "Priority": 2, "MinDelta": 0.05, "MaxDelta": 0.1, "MinPremium": 0.3, "WingWidth": 15, "ProfitTarget": 0.5, "StopLoss": 2.0}, "BrokenWingButterfly": {"Enabled": true, "Priority": 4, "MinPremium": 0.5, "ProfitTarget": 0.6, "StopLoss": 2.0}}, "MachineLearning": {"ModelUpdateIntervalHours": 24, "MinTrainingDataPoints": 100, "ConfidenceThreshold": 0.7, "SignalQualityWeights": {"ML": 0.4, "Technical": 0.3, "MarketCondition": 0.2, "Liquidity": 0.1}, "PredictionTimeframes": {"PriceDirection": "1h", "Volatility": "4h"}}, "Monitoring": {"UpdateIntervalMs": 5000, "AlertCheckIntervalMs": 10000, "HealthCheckIntervalMs": 30000, "MaxMetricsHistory": 1000, "NotificationChannels": {"Console": {"Enabled": true, "Priority": 1}, "Email": {"Enabled": true, "Priority": 2, "SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "Username": "<EMAIL>", "Password": "your-app-password", "ToAddress": "<EMAIL>", "UseSsl": true}, "SMS": {"Enabled": false, "Priority": 1, "Provider": "<PERSON><PERSON><PERSON>", "AccountSid": "", "AuthToken": "", "FromNumber": "", "ToNumber": "", "Region": ""}, "Slack": {"Enabled": false, "Priority": 3, "WebhookUrl": "", "Channel": "#alerts", "Username": "Zero DTE Bot", "IconEmoji": ":warning:"}, "Discord": {"Enabled": true, "Priority": 2, "BotToken": "MTM4MjE0OTM1MzQ3NjQ1NjQ5OA.GTJCg_.AqXUWgahLRKM6SQtuHFXYqWQnMj8wvX22WYzfM", "ChannelId": 1382148371103350799, "WebhookUrl": "", "Username": "Zero DTE Bot", "AvatarUrl": "", "UseEmbeds": true, "EnableSlashCommands": true}}}, "GuidanceRequest": {"Enabled": true, "ChatGptBotMention": "@ChatGptBot", "ChatGptBotUserId": "", "DefaultTimeout": "00:00:30", "DefaultMaxRetries": 3, "StoreResponses": true, "ResponseStoragePath": "GuidanceResponses", "LogRequests": true, "EnableRetryLogic": true}, "OpenAI": {"Enabled": true, "ApiKey": "***********************************************************************************************************************************************************************", "Model": "gpt-4o", "MaxTokens": 2000, "Temperature": 0.7, "SystemPrompt": "You are <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, a developer assistant who returns clear, well-structured technical instructions in response to requests from Augment. You are helping with a C# trading application called ZeroDateStrat that uses Alpaca API for 0 DTE options trading strategies.", "TriggerKeywords": ["!askchatgpt", "!chatgpt", "!gpt"], "EnableMentionTrigger": true, "EnableKeywordTrigger": true, "ResponsePrefix": "🤖 **ChatGPT Response:**\n", "ErrorMessage": "❌ Something went wrong while fetching the response. Please try again.", "EmptyRequestMessage": "⚠️ Please provide a valid request after the mention or command.", "MaxMessageLength": 2000}, "ChatGPTBot": {"Enabled": true, "BotToken": "", "ChannelId": 1382148371103350799, "Username": "ChatGPTBot", "EnableMentionTrigger": true, "EnableKeywordTrigger": true, "TriggerKeywords": ["!askchatgpt", "!chatgpt", "!gpt"], "ResponsePrefix": "🤖 **ChatGPT Response:**\n", "ErrorMessage": "⚠️ ChatGPTBot encountered an issue with the request. Try again in a few moments.", "EmptyRequestMessage": "⚠️ Please provide a valid request after the mention or command.", "MaxMessageLength": 2000, "EnablePriorityTagging": true, "EnableResponsePagination": true}, "CircuitBreaker": {"AlpacaAPI": {"FailureThreshold": 5, "TimeoutMinutes": 5}, "OptionsData": {"FailureThreshold": 3, "TimeoutMinutes": 3}, "MarketData": {"FailureThreshold": 3, "TimeoutMinutes": 2}, "RiskManagement": {"FailureThreshold": 2, "TimeoutMinutes": 1}, "OrderExecution": {"FailureThreshold": 2, "TimeoutMinutes": 1}}, "Optimization": {"MinIntervalHours": 24, "MinWinRate": 0.6, "MinSharpe": 1.0, "MaxDrawdown": 0.1, "PortfolioOptimization": {"Enabled": true, "RebalanceIntervalHours": 168, "MaxAllocationPerStrategy": 0.6, "MinAllocationPerStrategy": 0.1}, "AdaptiveParameters": {"Enabled": true, "UpdateIntervalHours": 12, "PerformanceThreshold": 0.05}}, "MultiTimeframe": {"Enabled": true, "Timeframes": ["1m", "5m", "15m", "1h", "1d"], "CacheExpiryMinutes": 5, "ConflictResolution": "HigherTimeframePriority"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "ZeroDateStrat": "Debug", "ZeroDateStrat.Services.AlpacaService": "Debug", "ZeroDateStrat.Services.RiskManager": "Debug", "ZeroDateStrat.Strategies": "Debug"}}}